{"App": {"clientName": "{clientName}", "appName": "{appName}"}, "Common": {"sidebar": {"main": "Main", "configurations": "Configurations", "navItems": {"overview": "Overview", "myWorkItems": "My Work Items"}}, "breadcrumbs": {"overview": "Overview", "i2r": "Ideation", "r2diag": "Technical Solutioning", "r2c": "Product Development", "r2q": "User Acceptance Testing", "myWorkItems": "Ongoing work items"}, "mobile": {"message": "Please open this website on your desktop browser to optimize the experience"}, "initialConfigModal": {"title": "Welcome to", "jiraConfigRequired": "Before you get started, please add your Jira configuration", "allConfigsRequired": "Before you get started, please add your Jira, Figma, and GitLab configurations", "buttonText": "Go to project configuration", "workbenchConfigRequired": "Before you proceed, please add your Workbench configuration", "confluenceConfigRequired": "Before you proceed, please add your Confluence configuration"}, "regenerationModal": {"title": "Tell us why you believe this response may not be the best suited for you", "placeholder": "Please specify...", "leftButtonText": "Regenerate", "rightButtonText": "Cancel", "regenerateAllDiagrams": "Regenerate all diagrams"}, "logoutModal": {"title": "Are you sure you want to logout?", "logoutButton": "Yes", "cancelButton": "No"}}, "Login": {"welcome": "Welcome to", "buttonText": "Login with SSO"}, "Homepage": {"moduleCards": {"i2r": {"title": "Ideation", "description": "Create requirements from an idea broken down by PRD, Epics and User Stories", "tags": {"i2r": "Idea to Requirements"}}, "r2diag": {"title": "Technical Solutioning", "description": "Create architecture diagrams and technical documents from user stories", "tags": {"r2diag": "Requirements to Diagram"}}, "r2d": {"title": "High Fidelity Designs", "description": "Create high fidelity designs from an idea or user stories based on DLS components", "tags": {"i2d": "Idea to Design", "r2d": "Requirements to Design"}}, "r2c": {"title": "Product Development & Validation", "description": "Generate context aware code from user stories and/or Figma designs", "tags": {"d2c": "Design to Code", "r2c": "Requirements to Code"}}, "r2q": {"title": "User Acceptance Testing", "description": "Create QA test cases based on user stories", "tags": {"r2q": "UI Test Assist"}}, "telemetry": {"title": "Post-Release Validation", "description": "Track the adoption of Generative AI tools", "tags": {"telemetry": "Telemetry Dashboard"}}}}, "MyWorkItems": {"heading": "Ongoing work items", "subHeading": "Tickets that are ready to be taken for development", "proceedButton": "Proceed", "error": "Sorry, something went wrong in fetching your ongoing work items", "noTasks": "No ongoing work items", "inputs": {"searchPlaceholder": "Search by ticket id or title", "moduleDropdownLabel": "<PERSON><PERSON><PERSON>", "moduleDropdownPlaceholder": "Select module", "moduleDropdownHelperText": "Choose the module into which the selected stories should be imported", "moduleDropdownError": "Please select a module", "storySelectionError": "Please select user stories to proceed", "storySelectionLimitError": "Please select no more than 5 tickets"}, "moduleDropdownItems": {"r2c": "Product Development", "r2cWorkbench": "Product Development - Workbench"}, "tableColumns": {"ticketId": "Ticket ID", "title": "Title", "labels": "Labels", "priority": "Priority", "dueDate": "Due Date"}}, "Platform": {"chat": {"you": "You", "r2c": "Product Development", "loading": "Loading message...", "error": "Sorry, something went wrong in loading your message", "selectedTickets": "Selected tickets", "additionalInput": "Additional input", "figmaLink": "Figma link", "contextRepositories": "Context repositories", "selectedRepository": "Selected repository", "branchName": "Branch"}, "requestHistory": {"title": "Recent Requests", "titlePlaceholder": "Search...", "noActiveRequests": "No active requests", "noArchivedRequests": "No archived requests", "noRequests": "No requests", "emptyTitleErrorMessage": "Chat title cannot be empty", "renameUnexpectedError": "Sorry, something went wrong while renaming the chat", "sameTitleErrorMessage": "Please update the chat title", "noSearchResultsFound": "No search results found", "tabs": {"active": "Active", "archived": "Archived"}, "states": {"active": "ACTIVE", "archived": "ARCHIVED"}, "options": {"archive": "Archive", "unarchive": "Unarchive", "delete": "Delete", "rename": "<PERSON><PERSON>"}, "createNewRequestButton": "Create a new request", "renameModal": {"title": "Please enter a new title for the chat", "inputPlaceholder": "Enter new title", "renameButton": "<PERSON><PERSON>", "cancelButton": "Cancel"}}, "optionsMenu": {"like": "Like", "dislike": "Dislike", "regenerate": "Regenerate", "edit": "Edit"}, "ticketSelectionModal": {"submitButton": "Submit", "cancelButton": "Cancel", "noTicketsFound": "No tickets found", "searchTickets": "Search tickets...", "epics": "Epics", "stories": "User Stories"}}, "Configurations": {"sidebar": {"title": "Settings", "tabs": {"jira": "<PERSON><PERSON>", "figma": "Figma", "gitlab": "GitLab", "workbench": "Workbench", "confluence": "Confluence"}}, "jira": {"title": "Jira configuration details", "notFoundError": "One or more Jira boards could not be accessed with the provided token.", "inputs": {"emailLabel": "Email", "emailPlaceholder": "Enter email", "emailErrorMessage": "Email is required", "tokenLabel": "Token", "tokenPlaceholder": "Enter token", "tokenErrorMessage": "Token is required", "invalidToken": "Invalid token", "invalidTokenOrEmail": "Invalid email or token", "boardIdLabel": "Board ID", "boardIdPlaceholder": "Enter board ID", "boardIdErrorMessage": "Board ID is required"}, "addMoreBoardsButton": "Add more boards", "saveButton": "Save changes", "successModal": {"title": "Project configuration is saved successfully!", "i2rButton": "Start Ideating", "homeButton": "Go home"}}, "figma": {"title": "Figma configuration details", "inputs": {"tokenLabel": "Figma token", "tokenPlaceholder": "Enter Figma token", "tokenErrorMessage": "Token is required", "updateTokenErrorMessage": "Please update the token"}, "saveButton": "Save changes", "successModal": {"title": "Project configuration is saved successfully!", "r2cButton": "Start Product Development", "homeButton": "Go home"}}, "gitlab": {"title": "Gitlab configuration details", "invalidTokenError": "<PERSON><PERSON> is invalid or has expired", "invalidHostError": "Invalid host format (eg: gitlab.com)", "invalidRepoError": "Repository doesn't exist or the given path is invalid (eg: group/project or group/subgroup/project)", "inputs": {"hostLabel": "Host", "hostPlaceholder": "Enter host url (eg: gitlab.com)", "hostErrorMessage": "Host is required", "updateHostErrorMessage": "Please update the host", "repoLabel": "Repository", "repoPlaceholder": "Enter repository name", "repoErrorMessage": "Repository is required", "updateRepoErrorMessage": "Please update the repository", "tokenLabel": "Token", "tokenPlaceholder": "Enter G<PERSON> token", "updateTokenErrorMessage": "Please update the token", "tokenErrorMessage": "Token is required", "duplicateRepositoryError": "Repository already exists"}, "syncStatus": {"success": "Repository ingested successfully", "inProgress": "Repository ingestion is in progress", "failed": "Repository ingestion failed. Re-save changes to try again"}, "addMoreReposButton": "Add more repositories", "saveButton": "Save changes", "successModal": {"title": "Project configuration is saved successfully!", "r2cButton": "Start Product Development", "homeButton": "Go home"}}, "workbench": {"title": "Workbench configuration details", "configCreationError": "Failed to create workbench configuration", "configUpdationError": "Failed to update workbench configuration", "inputs": {"tokenLabel": "Workbench token", "tokenPlaceholder": "Enter Workbench token", "tokenErrorMessage": "Token is required", "updateTokenErrorMessage": "Please update the token"}, "saveButton": "Save changes", "successModal": {"title": "Project configuration is saved successfully!", "r2cButton": "Start Product Development", "homeButton": "Go home"}}, "confluence": {"title": "Confluence configuration details", "configCreationError": "Failed to create confluence configuration", "configUpdationError": "Failed to update confluence configuration", "invalidTokenError": "<PERSON><PERSON> is invalid or has expired", "configSavedSuccessMessage": "Confluence configuration saved successfully", "inputs": {"tokenLabel": "Confluence token", "tokenPlaceholder": "Enter Confluence token", "tokenErrorMessage": "Token is required", "updateTokenErrorMessage": "Please update the token", "emailLabel": "Email", "emailPlaceholder": "Enter email", "emailErrorMessage": "Email is required", "updateEmailErrorMessage": "Please update the email", "spaceLabel": "Space ID", "spacePlaceholder": "Enter the space ID", "spaceErrorMessage": "Space ID is required", "updateSpaceErrorMessage": "Please update the space ID"}, "addMoreSpacesButton": "Add more spaces", "saveButton": "Save changes", "successModal": {"title": "Project configuration is saved successfully!", "r2cButton": "Start Technical Solutioning", "homeButton": "Go home"}}}, "I2R": {"homepage": {"title": "Ideation", "tabs": {"prd": "PRD", "epics": "Epics and Features", "stories": "User Stories"}, "inputs": {"subModule": {"label": "Generation type", "description": "Generate a PRD or epics and user stories", "options": {"prd": "PRD", "epicsAndUserStories": "Epics and User Stories"}}, "boardIds": {"label": "Board IDs", "placeholder": "Select board IDs for context", "description": "Context from all boards will be taken by default"}, "checkbox": {"label": "Start an idea from scratch"}, "idea": {"placeholder": "Enter your idea here...", "error": "Please enter an idea"}}, "sampleIdeas": "Sample ideas", "prompts": {"mobileAppInterface": {"title": "Build Mobile App Interface", "description": "Design the user interface and user experience for a mobile app, focusing on ease of navigation and accessibility"}, "responsiveWebApp": {"title": "Develop Responsive Web Page", "description": "Create a responsive web page layout optimized for different devices, ensuring a seamless user experience"}, "paymentGateway": {"title": "Integrate Payment Gateway", "description": "Develop and integrate secure payment gateway options for processing transactions across multiple platforms"}, "governmentServicePortal": {"title": "Create Government Service Portal", "description": "Build a portal for government services, enabling citizens to access and complete tasks online efficiently"}, "multiFactorAuthentication": {"title": "Implement Multi-factor Authentication", "description": "Set up multi-factor authentication for web and mobile platforms to enhance security for users"}, "userRegistrationOptimization": {"title": "Optimize User Registration Flow", "description": "Streamline the user registration and onboarding process for government or financial services, ensuring compliance and ease of use"}}}, "edit": {"title": "Title", "description": "Description", "descriptionPlaceholder": "Enter description", "acceptanceCriteria": "Acceptance Criteria", "analyticsTriggers": "Analytics Triggers", "saveButton": "Save changes", "cancelButton": "Cancel"}, "prd": {"error": "Sorry, something went wrong in generating the PRD", "content": "Content", "exportAsWordButton": "Export as Word", "generateEpicsAndUserStoriesButton": "Generate epics and user stories"}, "epics": {"heading": "Epics and Features", "exportCsv": "Export as CSV", "loading": "Loading epics", "error": "Sorry, something went wrong in generating epics", "versionSwitchingWhileRegenerationError": "Please wait for story generation to complete before switching versions"}, "userStories": {"heading": "User Stories", "acceptanceCriteria": "Acceptance Criteria", "analyticsTriggers": "Analytics Triggers", "noGeneratedStories": "No generated stories available.", "noRelevantStories": "No related stories available.", "error": "Sorry, something went wrong in generating user stories"}, "publish": {"singleStory": "Publish 1 User Story", "noStories": "Publish to Jira", "multipleStories": "Publish {storiesCount} User Stories", "noStoriesSelectedError": "No user stories to publish", "jiraLink": "View Jira link"}}, "R2C": {"heading": "Product Development & Validation", "sidebar": {"tabs": {"requestHistory": "Request History", "currentChat": "Current Chat"}}, "chat": {"input": {"placeholder": "Type a message...", "invalidInputError": "Please enter a valid message", "generationInProgressError": "Please wait for the current process to finish", "startCodingText": "Looks good! Let's start coding."}, "codePlanLoading": "Your code plan is being generated. This may take a few moments. Please wait.", "codeLoading": "Your code is being generated. This may take a few moments. Please wait.", "startCodingButton": "Start Coding"}, "storySelectionModal": {"title": "Select up to 5 user stories for generating code", "maxLimitError": "Please select no more than 5 tickets", "noStoriesFoundError": "No user stories found"}, "workbench": {"addStories": "+ Add stories", "noUserStories": "No user stories selected", "userStoriesSelectedMessage": "User stories selected", "workbenchGenerationButton": "Generate Workbench JSON", "workbenchRedirectBtn": "Go to Workbench", "workbenchProjectGenerationBtn": "Generate Workbench Application", "workbenchApplicationRedirectBtn": "Go to Workbench Application", "heading": "Product Development & Validation", "title": "Title", "titleRequired": "Title is required", "titlePlaceholder": "Enter title", "figmaLink": "Figma Link", "figmaLinkRequired": "Figma link is required", "figmaLinkPlaceholder": "Enter Figma link", "addFigmaLinkButton": "+ Add Figma link", "invalidFigmaLinkError": "Please enter a valid Figma link", "additionalInput": "Additional Input", "additionalInputPlaceholder": "Enter any additional information you would like to provide", "workbenchJsonGenerationError": "Sorry, something went wrong in generating the Workbench output", "defaultError": "Sorry, something went wrong. Please check the input and try again", "retryGenerationButton": "Retry", "retryGenerationError": "Sorry, something went wrong while retrying generating the Workbench application. Please try again", "workbenchJsonPublishError": "Sorry, something went wrong in publishing the Workbench application. Please try again", "invalidFigmaTokenError": "Your figma token has expired or you don't have access to the selected design file. Please reconfigure your Figma settings and try again", "invalidWorkbenchTokenError": "Your workbench token has expired or you don't have access to the selected project. Please reconfigure your Workbench settings and try again"}, "code": {"plannerTab": "Planner", "codePreviewTab": "Code Preview", "addStories": "+ Add stories", "noUserStories": "No user stories selected", "userStoriesSelectedMessage": "User stories selected", "codePlanGenerationBtn": "Generate Code Plan", "title": "Title", "titleRequired": "Title is required", "titlePlaceholder": "Enter title", "figmaLink": "Figma Link", "figmaLinkPlaceholder": "Enter Figma link", "invalidFigmaLinkError": "Please enter a valid Figma link", "branch": "Branch", "branchPlaceholder": "Enter branch name", "branchRequired": "Branch name is required", "additionalInput": "Additional Input", "generationTarget": "Platform Generation Target", "generationTargetMobile": "Mobile", "generationTargetOther": "Other", "additionalInputPlaceholder": "Enter any additional information you would like to provide", "mainRepository": "Main repository", "mainRepoSelectionRequired": "Main repository is required", "mainRepoSelectionDropdown": "Select the main repository", "contextRepositories": "Context repositories", "contextRepoSelectionDropdown": "Select repositories for additional context", "defaultError": "Sorry, something went wrong. Please check the input and try again", "codePlan": {"heading": "Generated Plan", "description": "Description:", "requirements": "Requirements:", "newTag": "New", "updatedTag": "Updated", "retryButton": "Retry"}, "errors": {"planGenerationError": "Sorry, something went wrong in generating the code plan, please try again", "generationErrorOutOfContext": "Sorry, your chat has exceeded the model maximum context limit, Please start a new chat", "codeSyncError": "Your tokens have expired or you don't have access to the selected repository. Please reconfigure your GitLab settings and try again", "codeSyncConnectionError": "Sorry, something went wrong while connecting to the repository. Please check your network and if the repository is reachable", "codeSyncFailedToCloneRepo": "Sorry, something went wrong in cloning the repository with the given branch name. Please make sure the selected branch is new or in sync with default", "figmaDesignNotFound": "Sorry, the Figma design was not found. Please check the link and try again", "figmaDesignInvalidToken": "Your figma token has expired or you don't have access to the selected design file. Please reconfigure your Figma settings and try again"}, "generatedCode": {"codeGenerationError": "Sorry, something went wrong in generating the code", "selectFileMessage": "// Select a file to preview its content", "mergeRequestBtn": "Merge <PERSON>", "commitBtn": "Commit", "syncBtn": "Sync", "cancelBtn": "Cancel", "mergeRequestModalBtn": "Create Merge <PERSON>", "mergeRequestTitlePlaceholder": "Enter a title for the merge request", "mergeRequestDescriptionPlaceholder": "Enter the description of the merge request", "commitSuccessMsg": "Code committed successfully", "syncSuccessMsg": "Code synced successfully", "mergeRequestSuccessMsg": "Merge request created successfully", "commitMsgPlaceholder": "Enter the commit message", "commitMsgModalHeading": "Please enter the following details to commit the code", "commitMsgRequired": "Commit message is required", "mergeRequestModalHeading": "Please enter the following details to raise a Merge Request", "commitMessageLabel": "Commit message", "mergeRequestTitleLabel": "Merge Request title", "mergeRequestDescriptionLabel": "Merge Request description", "viewMrLink": "View Merge Request", "actionFailed": "Request failed, please try again", "viewBranchLink": "Go to Branch"}}}, "R2Q": {"heading": "User Acceptance Testing", "subHeading": "QA Tests", "title": "Title", "titlePlaceholder": "Enter title", "additionalInput": "Additional Input", "additionalInputPlaceholder": "Enter any additional information you would like to provide", "noTicketsSelectedMessage": "No tickets selected", "requiredAsterisk": "*", "selectedStories": "User stories selected", "selectedEpics": "Epics selected", "generateQATestsButton": "Generate QA Tests", "errorMsg": "Sorry, something went wrong in generating QA tests", "noTicketsFoundErrorMsg": "Sorry, we were not able to find any user stories with the QA_READY label for the selected epic(s)", "invalidCommitUrlError": "Invalid commit URL provided. Please provide valid GitLab commit URLs.", "export": "Export as CSV", "gitlabLink": "GitLab Link", "*********************": "Enter Gitlab link", "gitlabLinkRequired": "GitLab link is required", "gitlabLinkInvalid": "Please enter a valid GitLab link", "addMoreGitlabLinksButton": "+ Add more GitLab links", "modal": {"selectTickets": "+ Add tickets", "epics": "Epics", "stories": "User Stories", "tasks": "Tasks", "bugs": "Bugs", "subBugs": "Sub-bugs", "subTasks": "Sub-tasks", "heading": "Select the epics or user stories for generating QA tests", "noTicketsFound": "No epics or user stories found"}, "edit": {"title": "Test Title", "description": "Test Description", "acceptanceCriteria": "Acceptance Criteria", "preconditions": "Preconditions", "testType": "Test Type", "testSteps": "Test Steps", "expectedResults": "Expected Results", "saveButton": "Save changes", "cancelButton": "Cancel"}, "testFields": {"preconditions": "Preconditions", "testType": "Test Type", "testSteps": "Test Steps", "expectedResults": "Expected Results", "description": "Test Description"}, "formInput": {"selectButton": "Select", "cancelButton": "Cancel"}, "publish": {"singleTest": "Publish 1 QA Test", "noTests": "Publish to Jira", "multipleTests": "Publish {testsCount} QA Tests", "noTestsSelectedError": "No QA Tests to publish", "jiraLink": "View Jira link", "zephyrLink": "View Zephyr link", "modal": {"body": {"title": "Publish to Jira or Zephyr", "jira": "<PERSON><PERSON>", "zephyr": "<PERSON><PERSON><PERSON><PERSON>", "publishTypeLabel": "Publish to", "publishTypePlaceholder": "Select Jira or Zephyr", "boardIdLabel": "Board ID", "boardIdPlaceholder": "Select board ID", "folderIdLabel": "Folder ID", "folderIdPlaceholder": "Select folder ID", "noBoardSelectedError": "Please select a board in order to publish to Zephyr", "noFoldersFoundError": "No folders were found. Please create one in order to publish to Zephyr", "fetchFoldersError": "Sorry, something went wrong in fetching the folders"}, "footer": {"publishButton": "Publish", "cancelButton": "Cancel", "noBoardSelectedError": "Please select a board in order to publish", "noFolderSelectedError": "Please select a folder to publish to Zephyr"}}}, "options": {"edit": "Edit", "like": "Like", "dislike": "Dislike", "regenerate": "Regenerate"}}, "R2Diag": {"title": "Technical Solutioning", "inputs": {"title": {"label": "Title", "placeholder": "Enter title", "error": "Please enter a title"}, "outputType": {"label": "Output Type", "placeholder": "Select what to generate", "options": {"diagrams": "Technical Diagrams", "technicalDocument": "Technical Document"}, "error": "Please select an output type"}, "diagramTypes": {"label": "Diagram Type", "placeholder": "Select which diagrams to generate", "options": {"sequenceDiagram": "Sequence Diagram", "systemArchitecture": "System Architecture", "erDiagram": "ER Diagram", "userFlowChart": "User Flow Chart", "infrastructureDiagram": "Infrastructure Diagram"}, "error": "Please select a diagram type"}, "epics": {"noTicketSelected": "No tickets selected", "ticketSelected": "Tickets Selected", "noEpicsSelected": "No epics selected", "epicsSelected": "Epics selected", "addEpicsButton": "+ Add epics", "modalTitle": "Select up to 5 epics for generating technical requirements", "modalNoTicketsFound": "No epics found", "modalTicketsError": "Please select no more than 5 epics"}, "technicalSpecifications": {"label": "Technical Specifications", "placeholder": "Enter any technical specifications you would like to provide"}, "generateButton": "Generate technical solution", "defaultError": "Sorry, something went wrong. Please check the input and try again", "templateSelectionModal": {"title": "Please choose a template for the technical document", "noTemplatesFound": "No templates found", "submitButton": "Generate Technical Document", "cancelButton": "Cancel"}, "templateFetchError": "Sorry, something went wrong in fetching the templates"}, "output": {"tabs": {"diagrams": "Diagrams", "technicalDocument": "Technical Document"}, "diagrams": {"diagramTypeFilter": "Diagram type", "mermaidCode": "Mermaid code", "diagram": "Diagram", "exportAsPngButton": "Export as PNG", "generateTechnicalDocumentButton": "Generate technical document", "error": "Sorry, something went wrong in generating diagrams", "regenerationError": "Sorry, something went wrong in diagram regeneration. Please try again", "editSaveButton": "Save", "editCancelButton": "Cancel", "exportFailedError": "Error exporting diagram as PNG", "retryGenerationButton": "Retry"}, "technicalDocument": {"content": "Content", "exportAsWordButton": "Export as Word", "publishToConfluenceButton": "Publish to Confluence", "publishError": "Sorry, something went wrong in publishing the technical document to Confluence", "viewConfluencePageButton": "View Confluence page", "error": "Sorry, something went wrong in generating the technical document. Please try again", "regenerationError": "Sorry, something went wrong in technical document regeneration. Please try again", "editSaveButton": "Save", "editCancelButton": "Cancel", "retryGenerationButton": "Retry", "invalidTokenError": "<PERSON><PERSON> is invalid or has expired"}}}}