import React, { createContext, useContext, useState, PropsWithChildren, useCallback } from 'react';
import { ChatMessage } from '@/modules/i2r/components/chat-box';

interface IChatContext {
  messages: ChatMessage[];
  setMessages: React.Dispatch<React.SetStateAction<ChatMessage[]>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  chatPollingEnabled: boolean;
  setChatPollingEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  handleSend: (msg: string) => Promise<void>;
  clearMessages: () => void;
  addMessage: (message: ChatMessage) => void;
  updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
  removeMessage: (id: string) => void;
}

const ChatContext = createContext<IChatContext>({} as IChatContext);

export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
};

export const ChatProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [chatPollingEnabled, setChatPollingEnabled] = useState(false);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  const addMessage = useCallback((message: ChatMessage) => {
    setMessages(prev => [...prev, message]);
  }, []);

  const updateMessage = useCallback((id: string, updates: Partial<ChatMessage>) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === id ? { ...msg, ...updates } : msg
      )
    );
  }, []);

  const removeMessage = useCallback((id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  }, []);

  const handleSend = useCallback(async (_msg: string) => {
    console.warn('handleSend not implemented - should be provided by useChatMessages hook');
  }, []);

  // Context value
  const contextValue: IChatContext = {
    messages,
    setMessages,
    isLoading,
    setIsLoading,
    chatPollingEnabled,
    setChatPollingEnabled,
    handleSend,
    clearMessages,
    addMessage,
    updateMessage,
    removeMessage,
  };

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
}; 