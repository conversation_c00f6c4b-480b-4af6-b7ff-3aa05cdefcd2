import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { Divider } from '@heroui/react';

import Textarea from '@/components/textarea';
import Button from '@/components/button';
import { ButtonVariant } from '@/components/button/types';
import FileUploadButton from '@/components/file-upload-button';
import TicketSelectionModal from '@/modules/platform/components/ticket-selection-modal';
import OptionsMenu from '../options-menu';

import { 
  PaperAirplaneIcon, 
  ArrowUpTrayIcon, 
  SparklesIcon 
} from '@heroicons/react/24/outline';

import axiosInstance from '@/utils/axios';
import { useI2RPollingContext } from '../../contexts/polling.context';
import { SubModules } from '@/modules/platform/interfaces/modules';
import { IJiraTicket } from '@/modules/platform/interfaces/tickets';

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai' | 'system';
  text: string;
  timestamp?: string;
  showActions?: boolean;
}

export interface ChatBoxProps {
  messages: ChatMessage[];
  onSend: (message: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  onLike?: (id: string) => void;
  onDislike?: (id: string) => void;
  onRegenerate?: (id: string) => void;
  onOpenRightPanel?: () => void;
  chatId: string;
}

const UserMessage: React.FC<{ message: ChatMessage }> = ({ message }) => (
  <div className="flex justify-end">
    <div className="bg-secondary-neutral-200 text-neutral-900 rounded-2xl px-4 py-3 max-w-md">
      <p className="text-sm leading-relaxed">{message.text}</p>
    </div>
  </div>
);

const AIMessage: React.FC<{ 
  message: ChatMessage; 
  prdData: any; 
  setPrdData: any; 
  onOpenRightPanel?: () => void; 
}> = ({ message, prdData, setPrdData, onOpenRightPanel }) => (
  <div className="flex justify-start">
    <div className="max-w-4xl w-full bg-white rounded-2xl p-4 border">
      <div className="flex items-center gap-2 mb-2">
        <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
          <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
        </div>
      </div>

      <p className="text-neutral-900 text-sm leading-relaxed">{message.text}</p>
      
      {message.showActions && (
        <>
          <Divider className="my-4" />
          <div className="flex gap-2 items-center">
            <OptionsMenu
              isLikeEnabled={prdData?.liked === null || !prdData?.liked}
              isDislikeEnabled={prdData?.liked === null || prdData?.liked}
              isRegenerateEnabled={true}
              isEditEnabled={false}
              showPublish={false}
              openRegenerationModal={() => {}}
              setRegenerationConfig={() => {}}
              type={SubModules.PRD}
              id={prdData?.id || ''}
              showOpen={true}
              onOpen={onOpenRightPanel}
              setPrdData={setPrdData}
              showEditButton={false}
            />
          </div>
        </>
      )}
    </div>
  </div>
);

const SuggestionSection: React.FC = () => (
  <div>
    <div className="flex items-center gap-2 mb-4">
      <div className="h-fit w-fit rounded-full border-1 border-secondary-neutral-200 bg-white p-1">
        <SparklesIcon className="h-5 w-5 text-primary-teal-600" />
      </div>
      <span className="text-sm text-gray-600">
        Hey there! Here are some next steps to consider:
      </span>
    </div>

    <div className="flex gap-2">
      <Button
        variant={ButtonVariant.FLAT}
        className="flex w-fit items-center gap-2 rounded-xl border px-3"
      >
        <div className="label-xs text-secondary-neutral-600">
          Generate Epics & User Stories
        </div>
      </Button>
      <Button
        variant={ButtonVariant.FLAT}
        className="flex w-fit items-center gap-2 rounded-xl border px-3"
      >
        <div className="label-xs text-secondary-neutral-600">
          Regenerate PRD
        </div>
      </Button>
    </div>
  </div>
);

const ChatInput: React.FC<{
  control: any;
  messageValue: string;
  onSend: (data: { message: string }) => void;
  onJiraIconClick: () => void;
  onFileSelect: (file: File) => void;
  placeholder: string;
  errorMessage: string;
}> = ({ 
  control, 
  messageValue, 
  onSend, 
  onJiraIconClick, 
  onFileSelect, 
  placeholder, 
  errorMessage 
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
    }
  };

  return (
    <div className="border border-gray-200 rounded-xl bg-white">
      <Controller
        name="message"
        control={control}
        rules={{ required: errorMessage }}
        render={({ field }) => (
          <div className="relative w-full">
            <Textarea
              {...field}
              placeholder={placeholder}
              className="rounded-xl border-none w-full resize-none focus:ring-0"
              onKeyDown={handleKeyDown}
              onKeyUp={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                }
              }}
            />
            
            {/* Left side icons */}
            <div className="absolute left-4 top-1/2 -translate-y-1/2 flex items-center gap-2 mt-2">
              <Image 
                className="cursor-pointer" 
                src="/icons/jira.svg" 
                alt="jira logo" 
                width={20} 
                height={20} 
                onClick={onJiraIconClick} 
              />
              <div className="w-px h-6 bg-secondary-neutral-300"></div>
              <FileUploadButton
                icon={<ArrowUpTrayIcon className="h-6 w-6 text-secondary-neutral-400" />}
                onFileSelect={onFileSelect}
              />
            </div>
            
            {/* Right side send button */}
            <div className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center mt-2">
              <PaperAirplaneIcon 
                className={`h-6 w-6 cursor-pointer ${
                  messageValue?.trim() 
                    ? 'text-primary-teal-600' 
                    : 'text-secondary-neutral-400'
                }`}
                onClick={() => onSend({ message: messageValue })}
              />
            </div>
          </div>
        )}
      />
    </div>
  );
};

const ChatBox: React.FC<ChatBoxProps> = ({
  messages,
  onSend,
  onOpenRightPanel,
  chatId
}) => {
  const { prdData, setPrdData } = useI2RPollingContext();
  const homepageConstants = useTranslations('I2R.homepage');
  
  const { control, handleSubmit, reset, watch } = useForm({
    defaultValues: { message: '' }
  });
  const messageValue = watch('message');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<'epics' | 'user-stories'>('epics');
  const [allAIReadyEpics, setAllAIReadyEpics] = useState<IJiraTicket[]>([]);
  const [allAIReadyStories, setAllAIReadyStories] = useState<IJiraTicket[]>([]);
  const [selectedEpics, setSelectedEpics] = useState<IJiraTicket[]>([]);
  const [selectedStories, setSelectedStories] = useState<IJiraTicket[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = useCallback((data: { message: string }) => {
    if (data.message.trim()) {
      onSend(data.message.trim());
      reset();
    }
  }, [onSend, reset]);

  const fetchTickets = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await axiosInstance.get('/api/platform/jira');
      const data = response.data || [];
      setAllAIReadyEpics(data.filter((t: IJiraTicket) => t.type.toLowerCase() === 'epic'));
      setAllAIReadyStories(data.filter((t: IJiraTicket) => t.type.toLowerCase() === 'user-story'));
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleJiraIconClick = useCallback(() => {
    setIsTicketModalOpen(true);
    if (!allAIReadyEpics.length && !allAIReadyStories.length) {
      fetchTickets();
    }
  }, [allAIReadyEpics.length, allAIReadyStories.length, fetchTickets]);

  const handleFileSelect = useCallback((file: File) => {
    console.log('Selected file:', file);
  }, []);

  const fetchPrd = useCallback(async (chatId: string) => {
    try {
      const response = await axiosInstance.get('/api/platform/requirement-document', {
        params: { chatId, documentType: SubModules.PRD },
      });
      setPrdData(response.data);
      return response.data;
    } catch (error) {
      return null;
    }
  }, [setPrdData]);

  useEffect(() => {
    if (messagesEndRef.current && typeof messagesEndRef.current.scrollIntoView === 'function') {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    fetchPrd(chatId);
  }, [chatId, fetchPrd]);

  const renderMessage = useCallback((message: ChatMessage) => {
    if (message.sender === 'user') {
      return <UserMessage message={message} />;
    }
    
    return (
      <AIMessage 
        message={message} 
        prdData={prdData} 
        setPrdData={setPrdData}
        onOpenRightPanel={onOpenRightPanel}
      />
    );
  }, [prdData, setPrdData, onOpenRightPanel]);

  return (
    <div className="flex flex-col h-full w-full space-y-6">
      {/* Messages */}
      <div className="flex-1 overflow-y-auto space-y-6">
        {messages.map((message) => (
          <div key={message.id} className="flex flex-col space-y-2">
            {renderMessage(message)}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <SuggestionSection />

      <ChatInput
        control={control}
        messageValue={messageValue}
        onSend={handleSendMessage}
        onJiraIconClick={handleJiraIconClick}
        onFileSelect={handleFileSelect}
        placeholder={homepageConstants('inputs.idea.placeholder')}
        errorMessage={homepageConstants('inputs.idea.error')}
      />

      {/* Ticket Selection Modal */}
      <TicketSelectionModal
        title="Select Jira Tickets"
        noTicketsError="No tickets found"
        maxLimitError="You can select up to 5 tickets"
        isOpen={isTicketModalOpen}
        onClose={() => setIsTicketModalOpen(false)}
        selectedOption={selectedOption}
        setSelectedOption={setSelectedOption}
        allAIReadyEpics={allAIReadyEpics}
        allAIReadyStories={allAIReadyStories}
        selectedEpics={selectedEpics}
        selectedStories={selectedStories}
        setSelectedEpics={setSelectedEpics}
        setSelectedStories={setSelectedStories}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ChatBox; 