import { useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import axiosInstance from '@/utils/axios';

import { useChatContext } from '../contexts/chat.context';

import { multiplyRefetchInterval } from '@/utils/multiply-refetch-interval';
import { areJobsComplete, hasJobFailed } from '@/modules/platform/utils/job-status';

import { Job } from '@/modules/platform/interfaces/job';
import { ChatMessage } from '@/modules/i2r/components/chat-box';

const LOADING_MESSAGE_TEXT = 'loading-';
const USER_MESSAGE_TEXT = 'user-';

interface UseChatMessagesProps {
  chatId: string;
  jiraConfig?: any;
}

interface FormattedMessage {
  id: string;
  sender: 'user' | 'ai';
  text: string;
  timestamp: string;
  showActions: boolean;
}

export function useChatMessages({ chatId, jiraConfig }: UseChatMessagesProps) {
  const {
    messages,
    setMessages,
    isLoading,
    setIsLoading,
    chatPollingEnabled,
    setChatPollingEnabled,
  } = useChatContext();

  const formatChatMessage = useCallback((msg: any): FormattedMessage => {
    const messageType = msg.messageType || msg.message_type || 'AI';
    const displayMessage = msg.displayMessage || msg.display_message || msg.text || '';
    const sender = messageType === 'HUMAN' ? 'user' : 'ai';
    
    return {
      id: msg.id,
      sender,
      text: displayMessage,
      timestamp: msg.created_at || new Date().toISOString(),
      showActions: messageType === 'AI',
    };
  }, []);

  const createUserMessage = useCallback((text: string): ChatMessage => ({
    id: `${USER_MESSAGE_TEXT}${Date.now()}-${Math.random()}`,
    sender: 'user',
    text,
  }), []);

  const createLoadingMessage = useCallback((): ChatMessage => ({
    id: `${LOADING_MESSAGE_TEXT}${Date.now()}-${Math.random()}`,
    sender: 'ai',
    text: 'Processing your request...',
    showActions: false,
  }), []);

  // API calls
  const fetchChatHistory = useCallback(async (chatId: string) => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.get('/api/platform/chat-message', {
        params: { chatId },
      });
      
      const chatHistory = response.data || [];
      const formattedMessages = chatHistory.map(formatChatMessage);
      setMessages(formattedMessages);
    } catch (error) {
      console.error('Error fetching chat history:', error);
      setMessages([]);
    } finally {
      setIsLoading(false);
    }
  }, [setMessages, setIsLoading, formatChatMessage]);

  const invokeChatAssistant = useCallback(async (message: string) => {
    if (!jiraConfig) return;
    
    await axiosInstance.post('/api/i2r/chat-assistant-invoke', {
      prompt: message,
      sub_type: 'CHAT',
      module: 'I2R',
      config_id: jiraConfig?.id,
      chat_id: chatId,
      i2r_metadata: {
        idea: 'string',
        idea_from_scratch: true,
      },
    });
  }, [jiraConfig, chatId]);

  // Job polling
  const fetchJobs = useCallback(async (): Promise<Job[]> => {
    try {
      const response = await axiosInstance.get('/api/platform/jobs', {
        params: { chatId },
      });
      
      const chatJobs = response.data.filter((job: Job) => job.sub_type === 'CHAT');
      
      // Handle completed jobs
      if (areJobsComplete(chatJobs) && chatPollingEnabled) {
        setChatPollingEnabled(false);
        await fetchChatHistory(chatId);
      }
      
      // Handle failed jobs
      const failedChatJobs = chatJobs.filter((job: Job) => hasJobFailed(job));
      if (failedChatJobs.length > 0 && chatPollingEnabled) {
        setChatPollingEnabled(false);
        setMessages((prev) => 
          prev.map(msg =>
            msg.id.startsWith(LOADING_MESSAGE_TEXT)
              ? { ...msg, text: 'Sorry, the request failed. Please try again.', showActions: false }
              : msg
          )
        );
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching jobs:', error);
      setChatPollingEnabled(false);
      return [];
    }
  }, [chatId, chatPollingEnabled, setChatPollingEnabled, fetchChatHistory, setMessages]);

  // Query for job polling
  useQuery<Job[], Error>({
    queryKey: ['fetchJobs', chatId],
    queryFn: () => fetchJobs(),
    enabled: !!chatId && chatPollingEnabled,
    refetchInterval: multiplyRefetchInterval,
  } as any);

  // Message sending handler
  const handleSend = useCallback(async (message: string) => {
    if (!jiraConfig) return;
    
    const userMessage = createUserMessage(message);
    setMessages((prev) => [...prev, userMessage]);
    
    const loadingMessage = createLoadingMessage();
    setMessages((prev) => [...prev, loadingMessage]);
    
    try {
      await invokeChatAssistant(message);
      setChatPollingEnabled(true);
    } catch (error) {
      console.error('Error sending message:', error);
      setMessages((prev) => prev.filter(msg => !msg.id.startsWith(LOADING_MESSAGE_TEXT)));
    }
  }, [jiraConfig, setMessages, createUserMessage, createLoadingMessage, invokeChatAssistant, setChatPollingEnabled]);

  useEffect(() => {
    if (!chatPollingEnabled) {
      setMessages((prev) => prev.filter(msg => !msg.id.startsWith(LOADING_MESSAGE_TEXT)));
    }
  }, [chatPollingEnabled, setMessages]);

  useEffect(() => {
    if (chatId) {
      fetchChatHistory(chatId);
    }
  }, [chatId, fetchChatHistory]);

  return { 
    messages, 
    isLoading, 
    handleSend 
  };
} 