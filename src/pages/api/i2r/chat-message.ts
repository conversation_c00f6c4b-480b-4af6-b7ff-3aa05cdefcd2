import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { API_PATHS } from '@/modules/platform/constants/api-endpoints';
import { getAuthToken } from '@/utils/auth-token';
import { getEnvConfig } from '@/env-config.zod';
import { formatChatMessagesResponse } from '@/utils/server/platform/chat/domain';

const BACKEND_URL = getEnvConfig().backendUrl;

async function handleGet(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { chatId } = req.query;
    const token = await getAuthToken(req);
    const apiBaseUrl = BACKEND_URL ?? '';

    const url = `${apiBaseUrl}/${API_PATHS.CHAT_MESSAGE}/${chatId}`;

    const response = await axios.get(url, {
      headers: {
        Authorization: token,
        'Content-Type': 'application/json',
      },
    });
    if (response === null) {
      console.log('getAllChatMessages returned null for chatId:', chatId);
      res.status(200).json([]);
      return;
    }
    const formattedResponse = formatChatMessagesResponse(response.data);
    res.status(200).json(formattedResponse);
  } catch (error) {
    console.error('Error in handleGet:', error);
    if (axios.isAxiosError(error)) {
      res.status(error.response?.status || 500).json({
        message: error.response?.data?.message || 'An error occurred while fetching data.',
      });
    } else {
      res.status(500).json({ message: 'An unexpected error occurred.' });
    }
  }
}

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'GET':
      return handleGet(req, res);

    default:
      res.setHeader('Allow', ['GET']);
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}