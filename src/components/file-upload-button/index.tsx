import React from 'react';

interface FileUploadButtonProps {
  onFileSelect: (file: File) => void;
  icon: React.ReactNode;
  accept?: string;
  className?: string;
}

const DEFAULT_ACCEPT = '.jpeg,.jpg,.png,.pdf';

const FileUploadButton: React.FC<FileUploadButtonProps> = ({
  onFileSelect,
  icon,
  accept = DEFAULT_ACCEPT,
  className = '',
}) => {
  return (
    <label className={`cursor-pointer ${className}`}>
      {icon}
      <input
        type="file"
        accept={accept}
        style={{ display: 'none' }}
        onChange={(e) => {
          const file = e.target.files?.[0];
          if (file) {
            const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
            if (!allowedTypes.includes(file.type)) {
              alert('Only JPEG, PNG, or PDF files are allowed.');
              e.target.value = '';
              return;
            }
            onFileSelect(file);
          }
        }}
      />
    </label>
  );
};

export default FileUploadButton; 